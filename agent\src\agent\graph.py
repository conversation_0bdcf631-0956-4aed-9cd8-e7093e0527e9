import os
import time

from langchain.chat_models import init_chat_model
from langgraph.prebuilt import create_react_agent
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.checkpoint.memory import MemorySaver

from contextlib import asynccontextmanager

from galileo import galileo_context
from galileo.handlers.langchain import GalileoAsyncCallback

from agent.util.prompt import PROMPT
from agent.util.tools import TOOLS
from agent.util.states import State
from dotenv import load_dotenv


'''#Initialize the Galileo callback handler
external_id = f"sales-agent-{int(time.time())}"
galileo_context.start_session(name="test", external_id=external_id)
galileo_callback = GalileoAsyncCallback()'''
load_dotenv()

@asynccontextmanager
async def make_graph():
    memory = MemorySaver()
    ''' mcp_client = MultiServerMCPClient(
        {
            "pricing": {
                "url": "http://mcp-pricing:8000/mcp/",
                "transport": "streamable_http",
            },
            "search": {
                "url": "http://mcp-search:8000/mcp/",
                "transport": "streamable_http",
            }
        }
    )'''

    # Gather all tools for the agent
    tools = []
    tools.extend(TOOLS)

    ''' pricing_tools = await mcp_client.get_tools(server_name="pricing")
    tools.extend(pricing_tools)

    search_tools = await mcp_client.get_tools(server_name="search")
    tools.extend(search_tools)'''

    #model_name = os.environ.get("CHAT_MODEL")
    chat_model = init_chat_model("google_genai:gemini-2.0-flash")

    agent = create_react_agent(
        model = chat_model,
        tools = tools,
        prompt = PROMPT,
        state_schema = State,
        checkpointer=memory,
    )
    yield agent
