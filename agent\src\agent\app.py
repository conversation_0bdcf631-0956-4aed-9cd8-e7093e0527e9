from datetime import datetime
from typing import Any
from dotenv import load_dotenv
import chainlit as cl
from galileo import galileo_context
from galileo.handlers.langchain import GalileoAsyncCallback

from agent.graph import make_graph
from langchain.schema.runnable import RunnableConfig
from langchain_core.messages import HumanMessage
from langchain_core.callbacks import Callbacks

load_dotenv()

agent = make_graph()

@cl.on_chat_start
async def on_chat_start() -> None:
    create_galileo_session()
    await cl.Message(content="Welcome to the Graphisoft sales assistant! How can I help you today?").send()


def create_galileo_session():
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    session_name = f"FSI Agent - {current_time}"
    galileo_context.start_session(name=session_name, external_id=cl.context.session.id)

    galileo_callback = GalileoAsyncCallback()
    cl.user_session.set("galileo_callback", galileo_callback)

    # Store session info in user session for later use
    cl.user_session.set("galileo_session_started", True)
    cl.user_session.set("session_name", session_name)

@cl.on_message
async def main(msg: cl.Message) -> None:
    """
    Handle the message from the user.

    param message: The message object containing user input.
    """
    # Create a config using the current Chainlit session ID. This is linked to the memory saver in the graph
    # to allow you to continue the conversation with the same context.
    config: dict[str, Any] = {"configurable": {"thread_id": cl.context.session.id}}

    # Prepare the final answer message to stream the response back to the user
    final_answer = cl.Message(content="")

    # Build the messages dictionary with the user's message
    messages: dict[str, Any] = {"messages": [HumanMessage(content=msg.content)]}

    # Create a callback handler to log the response to Galileo
    callbacks: Callbacks = []
    if cl.user_session.get("galileo_session_started", False):
        galileo_callback = cl.user_session.get("galileo_callback")
        callbacks: Callbacks = [galileo_callback]  # type: ignore
    else:
        print("Galileo session not started, using default callbacks.")

    # Set up the config for the streaming response
    runnable_config = RunnableConfig(callbacks=callbacks, **config)

    async with agent() as runnable:
        async for chunk in runnable.astream(
            messages, config=runnable_config
        ):
            # Handle the chunk structure from LangGraph streaming
            # The chunk is an AddableUpdatesDict containing node updates
            for node_name, node_output in chunk.items():
                if "messages" in node_output:
                    # Extract the last message content if it exists
                    messages_list = node_output["messages"]
                    if messages_list and hasattr(messages_list[-1], 'content'):
                        content = messages_list[-1].content
                        if content:
                            final_answer.content += content
                            await final_answer.stream()

    await final_answer.send()